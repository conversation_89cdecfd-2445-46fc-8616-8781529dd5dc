# 陶瓷储能材料数据分析工具

这是一个用于分析陶瓷储能材料性能数据的模块化Python工具。该工具专门用于比较不同成分间的差异与相同成分在不同电场下的差异。

## 功能特点

- **模块化设计**: 代码被重构为多个独立的类，便于维护和扩展
- **统计分析**: 提供变异系数计算和其他统计指标
- **自动化报告**: 生成详细的分析结果报告
- **无可视化依赖**: 移除了matplotlib等可视化库的依赖，专注于数据分析

## 主要模块

### 1. CeramicDataAnalyzer
数据加载和预处理器
- 加载Excel数据文件
- 清理缺失数据
- 计算归一化储能密度 (10000*Wrec/(E²))
- 提供基本统计信息

### 2. StatisticalCalculator
统计计算工具类
- `coefficient_of_variation()`: 计算变异系数
- `calculate_normalized_wrec()`: 计算归一化储能密度
- `basic_statistics()`: 计算基本统计量
- `analyze_within_group_variation()`: 分析组内变异

### 3. ComponentAnalyzer
成分差异分析器
- `analyze_between_components()`: 分析不同成分间的差异
- `analyze_within_components()`: 分析相同成分在不同电场下的差异

### 4. ResultAnalyzer
结果分析和比较器
- `compare_variations()`: 比较不同类型的变异系数
- `generate_conclusions()`: 生成分析结论

### 5. ReportGenerator
报告生成器
- `generate_summary_report()`: 生成详细的分析结果报告

## 使用方法

### 基本使用
```python
from ceramic_data_analysis import main

# 运行完整分析
results = main()
```

### 自定义使用
```python
from ceramic_data_analysis import (
    CeramicDataAnalyzer, 
    ComponentAnalyzer, 
    ResultAnalyzer, 
    ReportGenerator
)

# 1. 加载数据
analyzer = CeramicDataAnalyzer('your_data.xlsx')

# 2. 分析成分差异
component_analyzer = ComponentAnalyzer(analyzer.df_clean)
wrec_cv, eta_cv = component_analyzer.analyze_between_components()
within_stats = component_analyzer.analyze_within_components()

# 3. 比较结果
result_analyzer = ResultAnalyzer(analyzer.df_clean)
results = result_analyzer.compare_variations(wrec_cv, eta_cv, within_stats)

# 4. 生成报告
report_gen = ReportGenerator(analyzer.df_clean)
report_gen.generate_summary_report(results, within_stats)
```

## 数据格式要求

输入的Excel文件应包含以下列：
- `component`: 材料成分
- `E (kV cm-1)`: 电场强度
- `Wrec (J cm-3)`: 储能密度
- `η`: 储能效率
- `DOI`: 文献标识

## 输出文件

- `ceramic_analysis_results.txt`: 详细的分析结果报告

## 分析指标

### 变异系数 (CV)
- CV < 15%: 低变异
- 15% ≤ CV < 30%: 中等变异  
- CV ≥ 30%: 高变异

### 主要分析内容
1. **不同成分间差异**: 计算所有成分的平均性能，然后计算这些平均值的变异系数
2. **相同成分不同电场下差异**: 对每个成分-文献组合，计算其在不同电场下的变异系数
3. **差异倍数比较**: 比较两种差异的大小，验证成分对性能的影响是否大于电场的影响

## 依赖库

- pandas: 数据处理
- numpy: 数值计算
- openpyxl: Excel文件读取

## 安装依赖

```bash
pip install pandas numpy openpyxl
```

## 运行分析

```bash
python ceramic_data_analysis.py
```

## 设计原则

本工具遵循以下设计原则：
- **KISS (Keep It Simple, Stupid)**: 保持代码简洁明了
- **YAGNI (You Aren't Gonna Need It)**: 只实现当前需要的功能
- **SOLID原则**: 单一职责、开闭原则等面向对象设计原则

每个类都有明确的职责，便于测试和维护。
